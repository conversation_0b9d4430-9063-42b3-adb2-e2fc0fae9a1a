# Пример файла .env для frontrunner

# Обязательные параметры
API_KEY=your_api_key_here
API_SECRET=your_api_secret_here

# Основные параметры торговли
SYMBOL=BTCUSDT
QTY_USDT=100
VOL_PERELIVASHA_USDT=1000

# Дополнительные параметры (необязательные)
QTY_PROGREV_USDT=10
PROGREV_PERCENT=10
PROGREV_LOOP=true
PROGREV_INTERVAL=5
MULTIPLIER_PERELIVASHA=0.7
SHAG_CENI_MULTIPLIER=1.0
MAX_PRICE_DEVIATION=1.0
MIN_PRICE_DEVIATION=0.2
MAX_ORDERS=2
RECV_WINDOW=1000

# Новые параметры
ENABLE_MAX_PRICE_DEVIATION_REDUCTION=true
CANCEL_OLD_ORDERS=remove_after_timeout
CANCEL_TIMEOUT_SECONDS=1.5
