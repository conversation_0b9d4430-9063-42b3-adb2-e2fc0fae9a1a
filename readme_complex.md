# Frontrunner
language: golang

author: https://t.me/suenot

based on code: https://t.me/antonShcherbakov1

### Features:
- Load settings: first priority from params, second priority from .env
  - Example params: go run main.go -symbol MDTUSDT -qty 243000 -vol_perelivasha 2400000  -api_key XXXX -api_secret XXXX
  - Example .env:
  ```
    SYMBOL           = "MDTUSDT"
    QTY              = 243000
    VOL_PERELIVASHA   = 2400000
    SHAG_CENI         = 0.000005
    NUL_AFTER         = 6
    API_KEY           = "XXXX"
    API_SECRET        = "XXXX"
  ```
- Logging the execution speed of the order
- Checking the order status every second for 2 minutes

### Dev start
go run main.go -symbol XVGUSDT -qty 2900 -vol_perelivasha 50000  -direct-api 

### Build
rm -rf ./build
rm build.zip
mkdir -p build
GOOS=linux GOARCH=amd64 go build -o build/suenot-frontrunner main.go
GOOS=windows GOARCH=amd64 go build -o build/suenot-frontrunner.exe main.go
GOOS=darwin GOARCH=arm64 go build -o build/suenot-frontrunner.apple.silicon main.go
zip -r build.zip build/

### Start build
./build/suenot-frontrunner -symbol XVGUSDT -qty 2900 -vol_perelivasha 50000
или
./build/suenot-frontrunner.apple.silicon -symbol XVGUSDT -qty 2900 -vol_perelivasha 50000
или
suenot-frontrunner.exe -symbol XVGUSDT -qty 2900 -vol_perelivasha 50000


# Загрузка на сервер
scp -i "./suenot.pem" ./.env <EMAIL>:~/.env
scp -i "./suenot.pem" ./build.zip <EMAIL>:~/build.zip
ssh -i "./suenot.pem" <EMAIL>
### Подключаемся на сервер и распаковываем архив с билдами
unzip -o build.zip
chmod +x build/suenot-frontrunner
#### Запускаем скрипт
./build/suenot-frontrunner -symbol XVGUSDT -qty 2900 -vol_perelivasha 50000 

### Загрузка на домашний сервер
scp -P 4242 ./builds/frontrunner-golang-v0.0.15-progrev.zip root@**************:/var/www/frontrunner-golang-v0.0.15-progrev.zip
---

### macos
brew install util-linux
brew install fmt@9

### ubuntu
sudo apt-get update
sudo apt-get install libfmt9 libfmt-dev

### Загрузка на домашний сервер с++ версии
<надо сначала сделать zip>
scp -P 4242 ./frontrunner-linux-x86_64.zip root@**************:/var/www/frontrunner-linux-x86_64.zip

### Запуск с++
./frontrunner --symbol XVGUSDT --qty_progrev 2900 --progrev_percent 10 --qty 3000 --vol_perelivasha 50000 --shag_ceni 0.000075 -
---
mkdir fast && cd fast
wget http://gitlab.marketmaker.cc/frontrunner-linux-x86_64.zip
unzip frontrunner-linux-x86_64.zip
chmod +x frontrunner
echo -e 'API_KEY="j9vXJ70kYElCOUrKLA"\nAPI_SECRET="7o7u45d4D13t9Mzw6Xbehm7kk3Uzi6rbnCsu"' > .env
curl ifconfig.me
./frontrunner -symbol XVGUSDT -qty_progrev 2900 -progrev_percent 10 -qty 3000 -vol_perelivasha 50000 -shag_ceni 0.000075 

# Простарая загрузка бинарника на сервера и запуск
mkdir suenot-frontrunner-golang-v0.0.15-progrev && cd suenot-frontrunner-golang-v0.0.15-progrev
wget http://gitlab.marketmaker.cc/frontrunner-golang-v0.0.15-progrev.zip
unzip frontrunner-golang-v0.0.15-progrev.zip
mkdir suenot-frontrunner-golang-v0.0.15-progrev && cd suenot-frontrunner-golang-v0.0.15-progrev
wget http://gitlab.marketmaker.cc/frontrunner-golang-v0.0.15-progrev.zip
unzip frontrunner-golang-v0.0.15-progrev.zip
echo -e 'API_KEY="j9vXJ70kYElCOUrKLA"\nAPI_SECRET="7o7u45d4D13t9Mzw6Xbehm7kk3Uzi6rbnCsu"' > .env
curl ifconfig.me
./frontrunner-golang-v0.0.15-progrev/suenot-frontrunner -symbol XVGUSDT -qty_progrev 2900 -progrev_percent 10 -qty 2900 -vol_perelivasha 50000 -shag_ceni 0.000075 
### или
cd anton-progrev
./frontrunner-golang-v0.0.15-progrev/suenot-frontrunner -symbol XVGUSDT -qty 2900 -vol_perelivasha 50000  -direct-api
./frontrunner-golang-v0.0.15-progrev/suenot-frontrunner -symbol XVGUSDT -qty_progrev 2900 -progrev_percent 10 -qty 2900 -vol_perelivasha 50000 -shag_ceni 0.000075 
### или
cd anton-progrev
./frontrunner-golang-v0.0.15-progrev/suenot-frontrunner -symbol XVGUSDT -qty 2900 -vol_perelivasha 50000  -direct-api

wget http://gitlab.marketmaker.cc/frontrunner.zip
unzip frontrunner.zip


# сервера
# 

# i-01dabafe7100b74d3 ************* (ubuntu)) 6-41ms
ssh -i "suenot.pem" <EMAIL>
# i-07b83f1d5df2469ab ************* 15-41ms (last go version)
ssh -i "suenot.pem" <EMAIL>
# i-0d4a747c79d8ec1a1 ************* 15-36ms (last go version)
ssh -i "suenot.pem" <EMAIL>
# i-03a78f3b20b9f070d *********** 9-28ms (last go version)
ssh -i "suenot.pem" <EMAIL>
# i-0569693b8f0d623b3 ************ 6-18ms (last go version)
ssh -i "suenot.pem" <EMAIL>
# i-04c47333101e9b1f0 *********** 9-40ms (last go version)
ssh -i "suenot.pem" <EMAIL>
## 2cpu/4
# i-0deebeaeec74b5d04 ************ 13-30ms (last go version)
ssh -i "suenot.pem" <EMAIL>
# i-01b84586af8dbe358 ************ 7-23ms (last go version)
ssh -i "suenot.pem" <EMAIL>
# i-06f8b3a2ee4d5b3c5 ************* 6-15ms (last go version) -- пока лучший 8.5+8.7+7+7.4+9.6+7.4+8.36+7.9 (mean 8.1)
# i-06f8b3a2ee4d5b3c5 ************* 6-15ms (last go version) -- пока лучший 8.5+8.7+7+7.4+9.6+7.4+8.36+7.9 (mean 8.1)
ssh -i "suenot.pem" <EMAIL>
# i-0f678993537057442 172.31.46.88  10-19ms (last go version)
ssh -i "suenot.pem" <EMAIL>
# i-0bfce90f5da6c6745 172.31.34.141  11-16ms (last go version)
ssh -i "suenot.pem" <EMAIL>
---
# 172.31.41.16 6.72584, 12.193586, 7.404236, 7.333752, 8.203262, 7.008983
ssh -i "suenot.pem" <EMAIL>
# 172.31.33.215 7.996771, 7.78283, 8.528517, 6.35472, 9.234607, 11.960889
ssh -i "suenot.pem" <EMAIL>
# i-0b1f9ef631cc185c7 172.31.34.216 11.794438, 9.089894
ssh -i "suenot.pem" <EMAIL>
# i-063781276286a7f1d 172.31.43.153 6.79506 7.747153 19.852745 7.603598 5.942941 7.32525
ssh -i "suenot.pem" <EMAIL>
# i-07e0f776a6017aaca 172.31.34.121 7.919823 7.98522, 7.793087, 6.126883,  6.6420069999999996, 6.435752, 18.340008, 10.440688 5.524332, 7.254207
ssh -i "suenot.pem" <EMAIL>
# i-04920ab1f1f1eb632 172.31.42.27 6.71223 ms 7.470737 ms 6.606294 ms 8.163864 ms 5.512904 7.026137 7.073725 5.600452 -- пока лучший (mean 6.7)
ssh -i "suenot.pem" <EMAIL>
# i-076b320d2da5e97da 172.31.47.253 9.050053 8.134393 7.344087 7.350127 7.798914 8.248764
ssh -i "suenot.pem" <EMAIL>
# i-04724e6ada87dc62d 172.31.44.245 7.106914 6.698869 11.002337 7.36609 6.577607 7.481804
ssh -i "suenot.pem" <EMAIL>
# i-014237f1b5753d122 172.31.32.159 6.582981 9.24243 5.485754 6.4228439 8.968816 7.038831
ssh -i "suenot.pem" <EMAIL>
# i-06884572e2207f430 172.31.35.217 9.518063 ms 8.495965 7.753668 8.262154
ssh -i "suenot.pem" <EMAIL>
# i-0cf2d19b000179594 172.31.39.128 5.424595 ms 8.350703 ms 6.520905 ms 6.165278 ms 7.806641  6.931256 6.979534 6.943713 6.320382 6.855903 7.343935 5.74972 - хорош mean 6.78
ssh -i "suenot.pem" <EMAIL>
# i-01070af514a8dc202 172.31.33.229 6.466843 ms 5.980773  7.756802  5.088811  7.369386 7.325148 5.956154 6.976273  7.822907  10.353385 - mean 7
ssh -i "suenot.pem" <EMAIL> 9.377614 ms 8.672686 19.343218 7.381631
# i-06e6275f08d56c07e 172.31.36.116 7.072588 ms 6.272883 6.511108 9.176003 7.632811  7.167439 7.274644 7.743368999
ssh -i "suenot.pem" <EMAIL>

### (i-04724e6ada87dc62d, i-014237f1b5753d122, i-06884572e2207f430, i-0cf2d19b000179594, i-01070af514a8dc202, i-06e6275f08d56c07e)
---
# 172.31.41.16 6.72584, 12.193586, 7.404236, 7.333752, 8.203262, 7.008983
ssh -i "suenot.pem" <EMAIL>
# 172.31.33.215 7.996771, 7.78283, 8.528517, 6.35472, 9.234607, 11.960889
ssh -i "suenot.pem" <EMAIL>
# i-0b1f9ef631cc185c7 172.31.34.216 11.794438, 9.089894
ssh -i "suenot.pem" <EMAIL>
# i-063781276286a7f1d 172.31.43.153 6.79506 7.747153 19.852745 7.603598 5.942941 7.32525
ssh -i "suenot.pem" <EMAIL>
# i-07e0f776a6017aaca 172.31.34.121 7.919823 7.98522, 7.793087, 6.126883,  6.6420069999999996, 6.435752, 18.340008, 10.440688 5.524332, 7.254207
ssh -i "suenot.pem" <EMAIL>
# i-04920ab1f1f1eb632 172.31.42.27 6.71223 ms 7.470737 ms 6.606294 ms 8.163864 ms 5.512904 7.026137 7.073725 5.600452 -- пока лучший (mean 6.7)
ssh -i "suenot.pem" <EMAIL>
# i-076b320d2da5e97da 172.31.47.253 9.050053 8.134393 7.344087 7.350127 7.798914 8.248764
ssh -i "suenot.pem" <EMAIL>
# i-04724e6ada87dc62d 172.31.44.245 7.106914 6.698869 11.002337 7.36609 6.577607 7.481804
ssh -i "suenot.pem" <EMAIL>
# i-014237f1b5753d122 172.31.32.159 6.582981 9.24243 5.485754 6.4228439 8.968816 7.038831
ssh -i "suenot.pem" <EMAIL>
# i-06884572e2207f430 172.31.35.217 9.518063 ms 8.495965 7.753668 8.262154
ssh -i "suenot.pem" <EMAIL>
# i-0cf2d19b000179594 172.31.39.128 5.424595 ms 8.350703 ms 6.520905 ms 6.165278 ms 7.806641  6.931256 6.979534 6.943713 6.320382 6.855903 7.343935 5.74972 - хорош mean 6.78
ssh -i "suenot.pem" <EMAIL>
# i-01070af514a8dc202 172.31.33.229 6.466843 ms 5.980773  7.756802  5.088811  7.369386 7.325148 5.956154 6.976273  7.822907  10.353385 - mean 7
ssh -i "suenot.pem" <EMAIL> 9.377614 ms 8.672686 19.343218 7.381631
# i-06e6275f08d56c07e 172.31.36.116 7.072588 ms 6.272883 6.511108 9.176003 7.632811  7.167439 7.274644 7.743368999
ssh -i "suenot.pem" <EMAIL>

### (i-04724e6ada87dc62d, i-014237f1b5753d122, i-06884572e2207f430, i-0cf2d19b000179594, i-01070af514a8dc202, i-06e6275f08d56c07e)
<собрать список для подключения>

### Для запуска Антону
cd anton
./build/suenot-frontrunner -symbol XVGUSDT -qty 2900 -vol_perelivasha 50000 





### Usefull links
- https://github.com/bybit-exchange/api-usage-examples/blob/master/V5_demo/api_demo/Encryption_HMAC.go



# cpp version
### install on mac
```
brew install cmake boost openssl nlohmann-json mongo-cxx-driver cpprestsdk fmt@9
```

### run
```
./build/frontrunner --symbol XVGUSDT --qty 2900 --vol_perelivasha 50000
```
