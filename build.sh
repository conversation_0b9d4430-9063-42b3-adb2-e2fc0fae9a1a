#!/bin/bash

# Версия
VERSION="v0.0.25"
BUILD_DIR="builds/frontrunner-golang-$VERSION"

# Очистка старых сборок
rm -rf ./build
rm -f build.zip
rm -rf ./$BUILD_DIR
mkdir -p ./$BUILD_DIR

# Сборка для разных платформ
echo "Building for macOS..."
go build -o ./$BUILD_DIR/suenot-frontrunner.apple main.go

echo "Building for Windows..."
GOOS=windows GOARCH=amd64 go build -o ./$BUILD_DIR/suenot-frontrunner.exe main.go

echo "Building for Linux..."
GOOS=linux GOARCH=amd64 go build -o ./$BUILD_DIR/suenot-frontrunner main.go

# Создание архива
echo "Creating zip archive..."
cd builds
zip -r frontrunner-golang-$VERSION.zip frontrunner-golang-$VERSION/
cd ..

echo "Build complete! Files are in ./$BUILD_DIR/"
echo "Zip archive is in ./builds/frontrunner-golang-$VERSION.zip"
