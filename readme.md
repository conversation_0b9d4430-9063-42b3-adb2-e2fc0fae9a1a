# Bybit Frontrunner Bot

Бот для автоматической торговли на бирже Bybit, реализующий стратегию "frontrunning" (опережающая торговля).

## Описание

Бот подключается к WebSocket API биржи Bybit и отслеживает ордербук выбранной торговой пары. На основе анализа объемов и цен выставляет ордера на покупку или продажу, пытаясь опередить крупные рыночные движения. Работает только с фьючерсами <COIN>/USDT.

## Основные возможности

- Подключение к WebSocket API Bybit
- Гибкая настройка отслеживания ордербука через три потока данных (можно включать/отключать каждый поток):
  - Level 1 data (глубина 1) - частота обновления 10мс, используется для расчета срединной цены и границ отклонения
  - Level 50 data (глубина 50) - частота обновления 20мс, используется для анализа объемов
  - Level 200 data (глубина 200) - частота обновления 100мс, используется для анализа объемов (по умолчанию отключен)
- Автоматическое размещение ордеров
- Поддержка "прогревочных" ордеров с двумя режимами работы:
  - Одноразовый режим (по умолчанию) - выполняется один раз перед основной логикой
  - Циклический режим - выполняется каждые N секунд в фоновом режиме
- Работает в обоих направлениях (лонг и шорт)
- Настраиваемые параметры торговли
- Защита от ошибок и валидация параметров
- Поддержка двух режимов работы с API:
  - Через официальную библиотеку Bybit
  - Через fasthttp для минимальной латентности

## Настройка

### Необходимые параметры

Бот использует следующие параметры, которые можно задать через переменные окружения (.env файл) или аргументы командной строки:

- `SYMBOL` - торговая пара (например, "BTCUSDT")
- `QTY_USDT` - объем торговли в USDT
- `VOL_PERELIVASHA_USDT` - объем для отслеживания перелива в USDT
- `API_KEY` - API ключ Bybit
- `API_SECRET` - API секрет Bybit

### Дополнительные параметры
- `QTY_PROGREV_USDT` - объем для прогревочного ордера (по умолчанию 10 USDT)
- `PROGREV_PERCENT` - процент ниже средней цены для прогревочного ордера (по умолчанию 10%)
- `PROGREV_LOOP` - циклический режим прогревочного ордера (по умолчанию true)
- `PROGREV_INTERVAL` - интервал между прогревочными ордерами в секундах (по умолчанию 5)
- `MULTIPLIER_PERELIVASHA` - процент от объема перелива для срабатывания (по умолчанию 0.7 (т.е. 70% от объема перелива))
- `SHAG_CENI_MULTIPLIER` - множитель для шага цены (по умолчанию 1)
- `MAX_PRICE_DEVIATION` - максимальное отклонение цены от средней в процентах (по умолчанию 1.0%)
- `MIN_PRICE_DEVIATION` - минимальное отклонение цены от средней в процентах (по умолчанию 0.2%)
- `MAX_ORDERS` - максимальное количество выставленных ордеров (по умолчанию 2)
- `RECV_WINDOW` - receive window для API запросов в мс (по умолчанию 5000)
- `ENABLE_MAX_PRICE_DEVIATION_REDUCTION` - включить уменьшение MAX_PRICE_DEVIATION после исполнения ордеров манипулятора (по умолчанию true)
- `CANCEL_OLD_ORDERS` - управление старыми ордерами: "remove", "keep", "remove_after_timeout" (по умолчанию "remove_after_timeout")
- `CANCEL_TIMEOUT_SECONDS` - время ожидания перед снятием ордера в секундах (по умолчанию 1.5)

Эти параметры можно задать через флаги командной строки:

- `-qty_progrev_usdt` - объем для прогревочного ордера (по умолчанию 10 USDT)
- `-progrev_percent` - процент ниже средней цены для прогревочного ордера (по умолчанию 10%)
- `-progrev_loop` - циклический режим прогревочного ордера (по умолчанию true)
- `-progrev_interval` - интервал между прогревочными ордерами в секундах (по умолчанию 5)
- `-multiplier_perelivasha` - процент от объема перелива для срабатывания (по умолчанию 0.7 (т.е. 70% от объема перелива))
- `-shag_ceni_multiplier` - множитель для шага цены (по умолчанию 1)
- `-max_price_deviation` - максимальное отклонение цены от средней в процентах (по умолчанию 1.0%)
- `-min_price_deviation` - минимальное отклонение цены от средней в процентах (по умолчанию 0.2%)
- `-max_orders` - максимальное количество выставленных ордеров (по умолчанию 2)
- `-use_level1` - использовать Level 1 ордербук (по умолчанию true)
- `-use_level50` - использовать Level 50 ордербук (по умолчанию true)
- `-use_level200` - использовать Level 200 ордербук (по умолчанию false)
- `-direct_api` - использовать fasthttp вместо библиотеки bybit (по умолчанию false)
- `-recv_window` - receive window для API запросов в мс (по умолчанию 1000)
- `-enable_max_price_deviation_reduction` - включить уменьшение MAX_PRICE_DEVIATION после исполнения ордеров манипулятора (по умолчанию true)
- `-cancel_old_orders` - управление старыми ордерами: "remove", "keep", "remove_after_timeout" (по умолчанию "remove_after_timeout")
- `-cancel_timeout_seconds` - время ожидания перед снятием ордера в секундах (по умолчанию 1.5)

## Запуск

1. Создайте файл .env и заполните необходимые параметры (API_KEY и API_SECRET)
2. Запустите бота командой:

**Одноразовый прогревочный ордер:**
```bash
go run main.go -symbol XVGUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -qty_progrev_usdt 15 -progrev_percent 12 -progrev_loop=false -multiplier_perelivasha 0.8 -shag_ceni_multiplier 2 -max_price_deviation 1.5 -min_price_deviation 0.3 -max_orders 3 -use_level1=true -use_level50=true -use_level200=false -direct_api=true -recv_window=1000
```

**Циклический прогревочный ордер каждые 10 секунд (по умолчанию):**
```bash
go run main.go -symbol XVGUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000 -qty_progrev_usdt 15 -progrev_percent 12 -progrev_interval=10 -multiplier_perelivasha 0.8 -shag_ceni_multiplier 2 -max_price_deviation 1.5 -min_price_deviation 0.3 -max_orders 3 -use_level1=true -use_level50=true -use_level200=false -direct_api=true -recv_window=1000
```

Или с параметрами по умолчанию (MIN_PRICE_DEVIATION=0.2%, MAX_PRICE_DEVIATION=1.0%):
```bash
go run main.go -symbol XVGUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000
```

Или с циклическим прогревом каждые 5 секунд (по умолчанию):
```bash
go run main.go -symbol XVGUSDT -qty_usdt 100 -vol_perelivasha_usdt 1000
```

## Алгоритм работы

### Логика границ отклонения цены

Бот использует два параметра для определения границ анализа ордербука:
- **MIN_PRICE_DEVIATION** (по умолчанию 0.2%) - минимальное отклонение от срединной цены
- **MAX_PRICE_DEVIATION** (по умолчанию 1.0%) - максимальное отклонение от срединной цены

Для каждого направления торговли рассчитываются границы:
- **Для покупки (bids)**: bidMax ≤ цена ≤ bidMin (от дальней к ближней границе)
- **Для продажи (asks)**: askMin ≤ цена ≤ askMax (от ближней к дальней границе)

Это позволяет фильтровать сигналы только в определенном диапазоне отклонения от срединной цены.

### Адаптивное уменьшение MAX_PRICE_DEVIATION

Когда включена функция `ENABLE_MAX_PRICE_DEVIATION_REDUCTION` (по умолчанию включена), бот адаптивно уменьшает границы поиска ордеров манипулятора:

- После исполнения ордера манипулятора на одной стороне ордербука, бот ожидает его ордер на противоположной стороне
- Когда манипулятор убирает свой ордер, соответствующая MAX_PRICE_DEVIATION для этой стороны уменьшается на значение цены его прошлого ордера
- Это позволяет игнорировать большие ордера дальше от ордербука, так как манипулятор ставится все ближе к срединной цене

### Управление старыми ордерами

Бот поддерживает три режима работы со старыми неисполненными ордерами:
- **"remove"** - немедленно снимать старые ордера при размещении новых
- **"keep"** - оставлять старые ордера (может быть выставлено много ордеров одновременно)
- **"remove_after_timeout"** (по умолчанию) - снимать старые ордера через заданное время (по умолчанию 1.5 секунды)

```mermaid
flowchart TD
    %% Инициализация
    Start([Старт]) --> LoadEnv[Загрузка .env файла]
    LoadEnv --> ParseFlags[Парсинг флагов командной строки]
    ParseFlags --> ValidateParams{Проверка параметров}
    ValidateParams -->|Ошибка| Exit([Выход])

    %% Получение информации о символе
    ValidateParams -->|OK| GetSymbolInfo[Получение информации о символе]
    GetSymbolInfo --> ValidateSymbol{Валидация символа}
    ValidateSymbol -->|Ошибка| Exit

    %% Проверка необходимости прогревочного ордера
    ValidateSymbol -->|OK| CheckWarmup{Нужен прогревочный ордер?}

    %% Проверка режима прогревочного ордера
    CheckWarmup -->|Да| CheckWarmupMode{Циклический режим?}

    %% Одноразовый прогревочный ордер
    CheckWarmupMode -->|Нет| GetPrice[Получение текущей цены REST API]
    GetPrice --> CalcAvgPrice[Расчет средней цены]
    CalcAvgPrice --> ConvertQty[Конвертация USDT в количество монет]
    ConvertQty --> CalcWarmupPrice[Расчет цены прогрева]
    CalcWarmupPrice --> PlaceWarmup[Размещение прогревочного ордера]

    %% Циклический прогревочный ордер
    CheckWarmupMode -->|Да| StartWarmupLoop[Запуск горутины циклического прогрева]
    StartWarmupLoop --> PlaceFirstWarmup[Размещение первого прогревочного ордера]
    PlaceFirstWarmup --> StartTimer[Запуск таймера с интервалом]
    StartTimer --> WarmupLoop{Цикл прогрева}
    WarmupLoop --> PlaceLoopWarmup[Размещение циклического прогревочного ордера]
    PlaceLoopWarmup --> WarmupLoop

    %% Основной цикл - подписка на потоки
    CheckWarmup -->|Нет| SubscribeAll[Подписка на потоки]
    PlaceWarmup --> SubscribeAll
    StartWarmupLoop --> SubscribeAll

    SubscribeAll --> SubscribeL1[Подписка L1]
    SubscribeAll --> SubscribeL50[Подписка L50]
    SubscribeAll --> SubscribeL200[Подписка L200]

    %% Обработка L1 данных
    SubscribeL1 --> UpdateMediumPrice[Обновление срединной цены]
    UpdateMediumPrice --> UpdateDeviationPrices[Расчет границ: MIN_DEVIATION и динамические MAX_DEVIATION для каждой стороны]
    UpdateDeviationPrices --> CalcBoundaries[bidMin/Max, askMin/Max]

    %% Обработка L50/L200 данных
    SubscribeL50 --> CheckLastOrder{Проверка lastOrderType}
    SubscribeL200 --> CheckLastOrder

    %% Логика в зависимости от lastOrderType
    CheckLastOrder -->|Buy| CheckAskVolume{Объем > порога * multiplier<br/>И askMin ≤ цена ≤ askMax?}
    CheckLastOrder -->|Sell| CheckBidVolume{Объем > порога * multiplier<br/>И bidMax ≤ цена ≤ bidMin?}
    CheckLastOrder -->|Другое| CheckFirstOrder{Первый ордер?}

    CheckFirstOrder -->|Да| CheckBothVolumesFirst[Проверяем оба направления с полным объемом]
    CheckFirstOrder -->|Нет| CheckBothVolumes[Проверяем оба направления с multiplier]

    CheckBothVolumesFirst --> CheckBidVolumeFirst{Объем > порога<br/>И bidMax ≤ цена ≤ bidMin?}
    CheckBothVolumesFirst --> CheckAskVolumeFirst{Объем > порога<br/>И askMin ≤ цена ≤ askMax?}

    CheckBothVolumes --> CheckBidVolume
    CheckBothVolumes --> CheckAskVolume

    %% Логика ордеров для bids
    CheckBidVolume -->|Да| HandleOldOrders1[Обработка старых ордеров согласно флагу]
    CheckBidVolumeFirst -->|Да| HandleOldOrders1
    HandleOldOrders1 --> CalcBuyPrice[Цена = entriPrice + shagCeni * multiplier]
    CalcBuyPrice --> PlaceBuy[Размещение Buy ордера]
    PlaceBuy --> UpdateMaxDeviationBid{Включено уменьшение MAX_DEVIATION?}
    UpdateMaxDeviationBid -->|Да| ReduceMaxDeviationBid[Уменьшить maxPriceDeviationBid на значение entriPrice]
    UpdateMaxDeviationBid -->|Нет| CheckOrderCount
    ReduceMaxDeviationBid --> CheckOrderCount

    %% Логика ордеров для asks
    CheckAskVolume -->|Да| HandleOldOrders2[Обработка старых ордеров согласно флагу]
    CheckAskVolumeFirst -->|Да| HandleOldOrders2
    HandleOldOrders2 --> CalcSellPrice[Цена = entriPrice - shagCeni * multiplier]
    CalcSellPrice --> PlaceSell[Размещение Sell ордера]
    PlaceSell --> UpdateMaxDeviationAsk{Включено уменьшение MAX_DEVIATION?}
    UpdateMaxDeviationAsk -->|Да| ReduceMaxDeviationAsk[Уменьшить maxPriceDeviationAsk на значение entriPrice]
    UpdateMaxDeviationAsk -->|Нет| CheckOrderCount
    ReduceMaxDeviationAsk --> CheckOrderCount

    %% Проверка количества ордеров
    CheckOrderCount{Количество ордеров = maxOrders?}
    CheckOrderCount -->|Да| Exit

    %% Стили для тёмного фона
    classDef process fill:#2d5a27,stroke:#a3e635,stroke-width:2px,color:#fff
    classDef decision fill:#1e3a8a,stroke:#60a5fa,stroke-width:2px,color:#fff
    classDef terminal fill:#374151,stroke:#f472b6,stroke-width:2px,color:#fff
    classDef newFeature fill:#7c2d12,stroke:#fb923c,stroke-width:2px,color:#fff

    class Start,Exit terminal
    class ValidateParams,CheckWarmup,CheckWarmupMode,CheckBidVolume,CheckAskVolume,CheckLastOrder,CheckOrderCount,WarmupLoop,UpdateMaxDeviationBid,UpdateMaxDeviationAsk decision
    class LoadEnv,ParseFlags,GetSymbolInfo,GetPrice,CalcAvgPrice,ConvertQty,CalcWarmupPrice,PlaceWarmup,StartWarmupLoop,PlaceFirstWarmup,StartTimer,PlaceLoopWarmup,SubscribeAll,SubscribeL1,SubscribeL50,SubscribeL200,UpdateMediumPrice,UpdateDeviationPrices,CalcSellPrice,CalcBuyPrice,PlaceSell,PlaceBuy,CheckBothVolumes process
    class HandleOldOrders1,HandleOldOrders2,ReduceMaxDeviationBid,ReduceMaxDeviationAsk newFeature
```