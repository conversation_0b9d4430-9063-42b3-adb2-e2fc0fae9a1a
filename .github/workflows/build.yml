name: Build and Release

on:
  push:
    branches: [ main ]
    tags:
      - 'v*'
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Build
      run: |
        go build -v -o frontrunner-linux-amd64 main.go
        chmod +x frontrunner-linux-amd64

    - name: Create Archive
      if: startsWith(github.ref, 'refs/tags/')
      run: |
        VERSION=${GITHUB_REF#refs/tags/}
        zip -r frontrunner-${VERSION}-linux-amd64.zip frontrunner-linux-amd64 .env.example readme.md

    - name: Create Release
      uses: softprops/action-gh-release@v1
      if: startsWith(github.ref, 'refs/tags/')
      with:
        files: |
          frontrunner-*-linux-amd64.zip
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} 